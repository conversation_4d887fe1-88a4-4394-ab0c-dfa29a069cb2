#!/bin/bash

# GameFlex Auth Users Initialization Script
# This script creates development users in the Supabase auth.users table
# It runs after all services are up and the auth schema is ready

set -e

echo "🔐 Initializing GameFlex development users in Supabase Auth..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
until docker compose exec -T db pg_isready -U postgres -h localhost > /dev/null 2>&1; do
    echo "Database is not ready yet, waiting..."
    sleep 2
done

echo "✅ Database is ready!"

# Wait for auth service to be ready and create auth schema
echo "⏳ Waiting for auth service to initialize auth schema..."
sleep 10

# Function to insert a user into auth.users
insert_auth_user() {
    local user_id="$1"
    local email="$2"
    local password="$3"
    local display_name="$4"
    
    echo "👤 Creating auth user: $email"
    
    docker compose exec -T db psql -U postgres -d postgres -c "
    DO \$\$
    BEGIN
        -- Check if auth schema and users table exist
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'users') THEN
            -- Check if user already exists
            IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = '$user_id') THEN
                INSERT INTO auth.users (
                    id,
                    instance_id,
                    email,
                    encrypted_password,
                    email_confirmed_at,
                    created_at,
                    updated_at,
                    raw_app_meta_data,
                    raw_user_meta_data,
                    is_super_admin,
                    role
                ) VALUES (
                    '$user_id',
                    '00000000-0000-0000-0000-000000000000',
                    '$email',
                    crypt('$password', gen_salt('bf')),
                    NOW(),
                    NOW(),
                    NOW(),
                    '{\"provider\": \"email\", \"providers\": [\"email\"]}',
                    '{\"display_name\": \"$display_name\"}',
                    false,
                    'authenticated'
                );
                RAISE NOTICE 'Created auth user: $email';
            ELSE
                RAISE NOTICE 'Auth user already exists: $email';
            END IF;
        ELSE
            RAISE NOTICE 'Auth schema not ready yet for user: $email';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Error creating auth user $email: %', SQLERRM;
    END \$\$;
    " > /dev/null
}

# Create development users
insert_auth_user "00000000-0000-0000-0000-000000000001" "<EMAIL>" "devpassword123" "Development User"
insert_auth_user "00000000-0000-0000-0000-000000000002" "<EMAIL>" "adminpassword123" "Admin User"
insert_auth_user "00000000-0000-0000-0000-000000000003" "<EMAIL>" "johnpassword123" "John Doe"
insert_auth_user "00000000-0000-0000-0000-000000000004" "<EMAIL>" "janepassword123" "Jane Smith"
insert_auth_user "00000000-0000-0000-0000-000000000005" "<EMAIL>" "mikepassword123" "Mike Wilson"

# Verify users were created
echo "🔍 Verifying auth users were created..."
user_count=$(docker compose exec -T db psql -U postgres -d postgres -c "SELECT COUNT(*) FROM auth.users;" -t | tr -d ' ')

if [ "$user_count" -eq 5 ]; then
    echo "✅ Successfully created $user_count auth users!"
    echo ""
    echo "🎮 GameFlex Development Users:"
    echo "   📧 <EMAIL> (password: devpassword123)"
    echo "   📧 <EMAIL> (password: adminpassword123)"
    echo "   📧 <EMAIL> (password: johnpassword123)"
    echo "   📧 <EMAIL> (password: janepassword123)"
    echo "   📧 <EMAIL> (password: mikepassword123)"
    echo ""
    echo "🚀 You can now log in to GameFlex with any of these accounts!"
else
    echo "⚠️  Warning: Expected 5 users but found $user_count"
    echo "   This might be normal if some users already existed"
fi

echo "✅ Auth users initialization complete!"
